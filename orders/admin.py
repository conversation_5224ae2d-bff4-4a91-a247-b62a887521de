from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Sum, F, Q
from django.contrib.admin import SimpleListFilter
from django.utils import timezone
from .models import Payment, Order, OrderProduct


class PaymentMethodFilter(SimpleListFilter):
    title = 'Payment Method'
    parameter_name = 'payment_method'

    def lookups(self, request, model_admin):
        return (
            ('Cash on Delivery', 'Cash on Delivery'),
            ('ESewa', 'ESewa'),
            ('Khalti', 'Khalti'),
            ('Bank Transfer', 'Bank Transfer'),
            ('Fonepay', 'Fonepay'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(payment_method=self.value())


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('payment_id_display', 'user_display', 'payment_method_display', 'order_count', 'total_amount')
    list_filter = (PaymentMethodFilter,)
    search_fields = ('payment_id', 'user__email', 'user__first_name', 'user__last_name')
    readonly_fields = ('order_summary', 'payment_stats')
    ordering = ('-id',)
    list_per_page = 25
    verbose_name = "Customer Payment"
    verbose_name_plural = "Customer Payments"

    fieldsets = (
        ('Payment Information', {
            'fields': ('user', 'payment_id', 'payment_method')
        }),
        ('Payment Summary', {
            'fields': ('order_summary', 'payment_stats'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user').annotate(
            _order_count=Count('order'),
            _total_amount=Sum('order__grand_total')
        ).prefetch_related('order_set')
        return queryset

    def payment_id_display(self, obj):
        return format_html('<code>{}</code>', obj.payment_id)
    payment_id_display.short_description = 'Payment ID'
    payment_id_display.admin_order_field = 'payment_id'

    def user_display(self, obj):
        if obj.user:
            display_name = f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
            url = reverse('admin:account_useraccount_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, display_name)
        return format_html('<span style="color: #999;">No User</span>')
    user_display.short_description = 'User'
    user_display.admin_order_field = 'user__email'

    def payment_method_display(self, obj):
        method_colors = {
            'Cash on Delivery': '#28a745',
            'ESewa': '#007bff',
            'Khalti': '#6f42c1',
            'Bank Transfer': '#fd7e14',
            'Fonepay': '#20c997'
        }
        color = method_colors.get(obj.payment_method, '#6c757d')
        return format_html('<span style="color: {}; font-weight: bold;">{}</span>', color, obj.payment_method)
    payment_method_display.short_description = 'Payment Method'
    payment_method_display.admin_order_field = 'payment_method'

    def order_count(self, obj):
        count = obj._order_count
        if count > 0:
            url = reverse('admin:orders_order_changelist') + f'?payment__id__exact={obj.id}'
            return format_html('<a href="{}">{} orders</a>', url, count)
        return format_html('<span style="color: #999;">No orders</span>')
    order_count.short_description = 'Orders'
    order_count.admin_order_field = '_order_count'

    def total_amount(self, obj):
        total = obj._total_amount or 0
        if total > 0:
            total_formatted = f"Rs. {total:,}"
            return format_html('<strong>{}</strong>', total_formatted)
        return format_html('<span style="color: #999;">Rs. 0</span>')
    total_amount.short_description = 'Total Amount'
    total_amount.admin_order_field = '_total_amount'

    def order_summary(self, obj):
        orders = obj.order_set.all()
        if not orders:
            return format_html('<div style="color: #999;">No orders</div>')

        summary_html = '<div style="max-width: 500px;">'
        for order in orders[:5]:  # Show first 5 orders
            status_color = {
                'New': '#007bff',
                'Accepted': '#28a745',
                'Completed': '#6f42c1',
                'Cancelled': '#dc3545'
            }.get(order.status, '#6c757d')

            # Format the grand total separately to avoid format_html issues
            grand_total_formatted = f"Rs. {order.grand_total:,}"
            summary_html += f'''
                <div style="padding: 5px; border-bottom: 1px solid #eee;">
                    <strong>{order.order_number}</strong>
                    <span style="color: {status_color}; font-weight: bold;">[{order.status}]</span>
                    <span style="float: right;">{grand_total_formatted}</span>
                </div>
            '''
        if orders.count() > 5:
            summary_html += f'<div style="padding: 5px; color: #999;">... and {orders.count() - 5} more orders</div>'
        summary_html += '</div>'
        return format_html(summary_html)
    order_summary.short_description = 'Order Summary'

    def payment_stats(self, obj):
        orders = obj.order_set.all()
        if not orders:
            return format_html('<div style="color: #999;">No statistics available</div>')

        total_orders = orders.count()
        completed_orders = orders.filter(status='Completed').count()
        total_amount = sum(order.grand_total for order in orders)

        # Format values separately to avoid format_html issues
        total_amount_formatted = f"Rs. {total_amount:,}"
        success_rate_formatted = f"{(completed_orders/total_orders*100):.1f}%"

        stats_html = f'''
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div><strong>Total Orders:</strong> {total_orders}</div>
                <div><strong>Completed:</strong> {completed_orders}</div>
                <div><strong>Total Amount:</strong> {total_amount_formatted}</div>
                <div><strong>Success Rate:</strong> {success_rate_formatted}</div>
            </div>
        '''
        return format_html(stats_html)
    payment_stats.short_description = 'Payment Statistics'


class OrderStatusFilter(SimpleListFilter):
    title = 'Order Status'
    parameter_name = 'order_status'

    def lookups(self, request, model_admin):
        return (
            ('New', 'New Orders'),
            ('Accepted', 'Accepted Orders'),
            ('Completed', 'Completed Orders'),
            ('Cancelled', 'Cancelled Orders'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(status=self.value())


class OrderValueFilter(SimpleListFilter):
    title = 'Order Value'
    parameter_name = 'order_value'

    def lookups(self, request, model_admin):
        return (
            ('low', 'Under Rs. 1,000'),
            ('medium', 'Rs. 1,000 - 5,000'),
            ('high', 'Rs. 5,000 - 10,000'),
            ('premium', 'Over Rs. 10,000'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'low':
            return queryset.filter(grand_total__lt=1000)
        elif self.value() == 'medium':
            return queryset.filter(grand_total__range=(1000, 5000))
        elif self.value() == 'high':
            return queryset.filter(grand_total__range=(5000, 10000))
        elif self.value() == 'premium':
            return queryset.filter(grand_total__gt=10000)


class OrderProductInline(admin.TabularInline):
    model = OrderProduct
    extra = 0
    fields = ('product', 'quantity', 'product_price', 'subtotal_display', 'ordered')
    readonly_fields = ('subtotal_display',)
    classes = ('collapse',)

    def subtotal_display(self, obj):
        if obj.pk:
            subtotal = obj.quantity * obj.product_price
            subtotal_formatted = f"Rs. {subtotal:,}"
            return format_html('<strong>{}</strong>', subtotal_formatted)
        return "-"
    subtotal_display.short_description = 'Subtotal'


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('order_number_display', 'customer_info', 'status_display', 'payment_method_display',
                   'grand_total_display', 'order_items_count', 'is_ordered', 'status', 'created_at')
    list_filter = (OrderStatusFilter, OrderValueFilter, 'is_ordered', 'payment__payment_method', 'created_at')
    search_fields = ('order_number', 'first_name', 'last_name', 'email', 'phone', 'user__email')
    list_editable = ('status',)
    readonly_fields = ('created_at', 'upated_at', 'order_summary', 'customer_details', 'shipping_address')
    ordering = ('-created_at',)
    list_per_page = 20
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Order Information', {
            'fields': ('order_number', 'user', 'payment', 'status', 'is_ordered')
        }),
        ('Customer Information', {
            'fields': ('first_name', 'last_name', 'email', 'phone', 'customer_details')
        }),
        ('Shipping Address', {
            'fields': ('state', 'area', 'address', 'shipping_address'),
            'classes': ('collapse',)
        }),
        ('Order Details', {
            'fields': ('grand_total', 'tax', 'order_note', 'order_summary')
        }),
        ('Technical Details', {
            'fields': ('ip', 'created_at', 'upated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [OrderProductInline]
    actions = ['mark_as_accepted', 'mark_as_completed', 'mark_as_cancelled', 'mark_as_ordered']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user', 'payment').annotate(
            _items_count=Count('orderproduct')
        ).prefetch_related('orderproduct_set__product')
        return queryset

    def order_number_display(self, obj):
        return format_html('<strong><code>{}</code></strong>', obj.order_number)
    order_number_display.short_description = 'Order Number'
    order_number_display.admin_order_field = 'order_number'

    def customer_info(self, obj):
        name = obj.full_name()
        if obj.user:
            url = reverse('admin:account_useraccount_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a><br><small>{}</small>', url, name, obj.email)
        return format_html('{}<br><small>{}</small>', name, obj.email)
    customer_info.short_description = 'Customer'
    customer_info.admin_order_field = 'first_name'

    def status_display(self, obj):
        status_colors = {
            'New': '#007bff',
            'Accepted': '#28a745',
            'Completed': '#6f42c1',
            'Cancelled': '#dc3545'
        }
        color = status_colors.get(obj.status, '#6c757d')
        return format_html('<span style="color: {}; font-weight: bold; padding: 3px 8px; border-radius: 3px; background: {}20;">{}</span>',
                          color, color, obj.status)
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'status'

    def payment_method_display(self, obj):
        if obj.payment:
            method_colors = {
                'Cash on Delivery': '#28a745',
                'ESewa': '#007bff',
                'Khalti': '#6f42c1',
                'Bank Transfer': '#fd7e14',
                'Fonepay': '#20c997'
            }
            color = method_colors.get(obj.payment.payment_method, '#6c757d')
            url = reverse('admin:orders_payment_change', args=[obj.payment.pk])
            return format_html('<a href="{}" style="color: {}; font-weight: bold;">{}</a>',
                             url, color, obj.payment.payment_method)
        return format_html('<span style="color: #999;">No Payment</span>')
    payment_method_display.short_description = 'Payment Method'

    def grand_total_display(self, obj):
        grand_total_formatted = f"Rs. {obj.grand_total:,}"
        return format_html('<strong>{}</strong>', grand_total_formatted)
    grand_total_display.short_description = 'Total'
    grand_total_display.admin_order_field = 'grand_total'

    def order_items_count(self, obj):
        count = obj._items_count
        if count > 0:
            url = reverse('admin:orders_orderproduct_changelist') + f'?order__id__exact={obj.id}'
            return format_html('<a href="{}">{} items</a>', url, count)
        return format_html('<span style="color: #999;">No items</span>')
    order_items_count.short_description = 'Items'
    order_items_count.admin_order_field = '_items_count'

    def customer_details(self, obj):
        details_html = f'''
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div><strong>Name:</strong> {obj.full_name()}</div>
                <div><strong>Email:</strong> {obj.email}</div>
                <div><strong>Phone:</strong> {obj.phone}</div>
                <div><strong>IP Address:</strong> {obj.ip or 'Not recorded'}</div>
            </div>
        '''
        return format_html(details_html)
    customer_details.short_description = 'Customer Details'

    def shipping_address(self, obj):
        address_html = f'''
            <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                <div><strong>State:</strong> {obj.state}</div>
                <div><strong>Area:</strong> {obj.area}</div>
                <div><strong>Address:</strong> {obj.address}</div>
            </div>
        '''
        return format_html(address_html)
    shipping_address.short_description = 'Shipping Address'

    def order_summary(self, obj):
        items = obj.orderproduct_set.all()
        if not items:
            return format_html('<div style="color: #999;">No items in order</div>')

        summary_html = '<div style="max-width: 500px;">'
        total_items = 0
        for item in items:
            total_items += item.quantity
            # Format the item subtotal separately to avoid format_html issues
            item_subtotal_formatted = f"Rs. {item.quantity * item.product_price:,}"
            summary_html += f'''
                <div style="padding: 5px; border-bottom: 1px solid #eee;">
                    <strong>{item.product.name}</strong>
                    <span style="color: #666;">x{item.quantity}</span>
                    <span style="float: right;">{item_subtotal_formatted}</span>
                </div>
            '''

        # Format the totals separately to avoid format_html issues
        tax_formatted = f"Rs. {obj.tax:,.2f}"
        grand_total_formatted = f"Rs. {obj.grand_total:,}"

        summary_html += f'''
            <div style="padding: 10px; background: #e9ecef; margin-top: 5px; border-radius: 3px;">
                <div><strong>Total Items:</strong> {total_items}</div>
                <div><strong>Tax:</strong> {tax_formatted}</div>
                <div><strong>Grand Total:</strong> {grand_total_formatted}</div>
            </div>
        '''
        summary_html += '</div>'
        return format_html(summary_html)
    order_summary.short_description = 'Order Summary'

    # Custom actions
    def mark_as_accepted(self, request, queryset):
        updated = queryset.update(status='Accepted')
        self.message_user(request, f'{updated} orders marked as accepted.')
    mark_as_accepted.short_description = "Mark selected orders as accepted"

    def mark_as_completed(self, request, queryset):
        updated = queryset.update(status='Completed')
        self.message_user(request, f'{updated} orders marked as completed.')
    mark_as_completed.short_description = "Mark selected orders as completed"

    def mark_as_cancelled(self, request, queryset):
        updated = queryset.update(status='Cancelled')
        self.message_user(request, f'{updated} orders marked as cancelled.')
    mark_as_cancelled.short_description = "Mark selected orders as cancelled"

    def mark_as_ordered(self, request, queryset):
        updated = queryset.update(is_ordered=True)
        self.message_user(request, f'{updated} orders marked as ordered.')
    mark_as_ordered.short_description = "Mark selected orders as ordered"


class OrderProductStatusFilter(SimpleListFilter):
    title = 'Order Status'
    parameter_name = 'order_status'

    def lookups(self, request, model_admin):
        return (
            ('ordered', 'Ordered Items'),
            ('not_ordered', 'Not Ordered Items'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'ordered':
            return queryset.filter(ordered=True)
        elif self.value() == 'not_ordered':
            return queryset.filter(ordered=False)


@admin.register(OrderProduct)
class OrderProductAdmin(admin.ModelAdmin):
    list_display = ('product_link', 'order_link', 'user_display', 'quantity_display',
                   'price_display', 'subtotal_display', 'variations_display', 'ordered_status', 'ordered', 'created_at')
    list_filter = (OrderProductStatusFilter, 'ordered', 'product__category', 'created_at')
    search_fields = ('product__name', 'order__order_number', 'user__email', 'user__first_name', 'user__last_name')
    list_editable = ('ordered',)
    readonly_fields = ('created_at', 'updated_at', 'subtotal_display', 'variations_summary')
    ordering = ('-created_at',)
    list_per_page = 25
    date_hierarchy = 'created_at'
    verbose_name = "Order Item"
    verbose_name_plural = "Order Items"

    fieldsets = (
        ('Product Information', {
            'fields': ('product', 'quantity', 'product_price', 'subtotal_display')
        }),
        ('Order Information', {
            'fields': ('order', 'user', 'payment', 'ordered')
        }),
        ('Variations', {
            'fields': ('variation', 'variations_summary'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ('variation',)
    actions = ['mark_as_ordered', 'mark_as_not_ordered']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'product', 'order', 'user', 'payment'
        ).prefetch_related('variation')

    def product_link(self, obj):
        url = reverse('admin:products_product_change', args=[obj.product.pk])
        return format_html('<a href="{}">{}</a>', url, obj.product.name)
    product_link.short_description = 'Product'
    product_link.admin_order_field = 'product__name'

    def order_link(self, obj):
        url = reverse('admin:orders_order_change', args=[obj.order.pk])
        return format_html('<a href="{}"><code>{}</code></a>', url, obj.order.order_number)
    order_link.short_description = 'Order'
    order_link.admin_order_field = 'order__order_number'

    def user_display(self, obj):
        display_name = f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
        url = reverse('admin:account_useraccount_change', args=[obj.user.pk])
        return format_html('<a href="{}">{}</a>', url, display_name)
    user_display.short_description = 'User'
    user_display.admin_order_field = 'user__email'

    def quantity_display(self, obj):
        if obj.quantity > 5:
            return format_html('<span style="color: orange; font-weight: bold;">{}</span>', obj.quantity)
        return str(obj.quantity)
    quantity_display.short_description = 'Qty'
    quantity_display.admin_order_field = 'quantity'

    def price_display(self, obj):
        price_formatted = f"Rs. {obj.product_price:,}"
        return format_html('<strong>{}</strong>', price_formatted)
    price_display.short_description = 'Unit Price'
    price_display.admin_order_field = 'product_price'

    def subtotal_display(self, obj):
        subtotal = obj.quantity * obj.product_price
        subtotal_formatted = f"Rs. {subtotal:,}"
        return format_html('<strong>{}</strong>', subtotal_formatted)
    subtotal_display.short_description = 'Subtotal'

    def variations_display(self, obj):
        variations = obj.variation.all()
        if variations:
            var_list = [f"{v.variation_category}: {v.variation_value}" for v in variations[:2]]
            display = ", ".join(var_list)
            if variations.count() > 2:
                display += f" (+{variations.count() - 2} more)"
            return format_html('<span style="font-size: 11px; color: #666;">{}</span>', display)
        return format_html('<span style="color: #999;">No variations</span>')
    variations_display.short_description = 'Variations'

    def variations_summary(self, obj):
        variations = obj.variation.all()
        if not variations:
            return format_html('<div style="color: #999;">No variations selected</div>')

        summary_html = '<div style="max-width: 400px;">'
        for variation in variations:
            summary_html += f'''
                <div style="padding: 3px; border-bottom: 1px solid #eee;">
                    <strong>{variation.variation_category}:</strong> {variation.variation_value}
                </div>
            '''
        summary_html += '</div>'
        return format_html(summary_html)
    variations_summary.short_description = 'Variation Details'

    def ordered_status(self, obj):
        if obj.ordered:
            return format_html('<span style="color: green;">✓ Ordered</span>')
        return format_html('<span style="color: red;">✗ Not Ordered</span>')
    ordered_status.short_description = 'Status'
    ordered_status.admin_order_field = 'ordered'

    def mark_as_ordered(self, request, queryset):
        updated = queryset.update(ordered=True)
        self.message_user(request, f'{updated} order products marked as ordered.')
    mark_as_ordered.short_description = "Mark selected items as ordered"

    def mark_as_not_ordered(self, request, queryset):
        updated = queryset.update(ordered=False)
        self.message_user(request, f'{updated} order products marked as not ordered.')
    mark_as_not_ordered.short_description = "Mark selected items as not ordered"
