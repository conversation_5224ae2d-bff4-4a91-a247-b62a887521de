from django.db.models import Count
from django_filters.rest_framework import DjangoFilter<PERSON>ackend
from rest_framework import permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.response import Response

from orders.models import Order, OrderProduct, Payment
from orders.serializers import (
    OrderCreateSerializer,
    OrderListSerializer,
    OrderProductCreateSerializer,
    OrderProductListSerializer,
    OrderProductSerializer,
    OrderSerializer,
    PaymentCreateSerializer,
    PaymentListSerializer,
    PaymentSerializer,
)


class PaymentViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Payment model
    Provides CRUD operations for payments
    """

    queryset = Payment.objects.select_related("user")
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = ["payment_id", "user__email", "user__first_name", "user__last_name"]
    ordering_fields = ["payment_id", "payment_method"]
    ordering = ["-id"]
    filterset_fields = ["payment_method", "user"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return PaymentListSerializer
        elif self.action == "create":
            return PaymentCreateSerializer
        return PaymentSerializer

    def get_queryset(self):
        """Filter payments based on user permissions"""
        queryset = self.queryset.annotate(_order_count=Count("order"))

        if self.request.user.is_staff:
            return queryset  # Staff can see all payments
        else:
            # Regular users can only see their own payments
            return queryset.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Set user when creating payment"""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=["get"])
    def orders(self, request, pk=None):
        """Get all orders for this payment"""
        payment = self.get_object()
        orders = Order.objects.filter(payment=payment).select_related("user")

        serializer = OrderListSerializer(
            orders, many=True, context={"request": request}
        )
        return Response(serializer.data)


class OrderViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Order model
    Provides CRUD operations for orders
    """

    queryset = Order.objects.select_related("user", "payment").prefetch_related(
        "orderproduct_set__product"
    )
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = [
        "order_number",
        "first_name",
        "last_name",
        "email",
        "phone",
        "user__email",
    ]
    ordering_fields = ["created_at", "grand_total", "status"]
    ordering = ["-created_at"]
    filterset_fields = ["status", "is_ordered", "payment__payment_method", "user"]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return OrderListSerializer
        elif self.action == "create":
            return OrderCreateSerializer
        return OrderSerializer

    def get_queryset(self):
        """Filter orders based on user permissions"""
        queryset = self.queryset.annotate(_items_count=Count("orderproduct"))

        if self.request.user.is_staff:
            return queryset  # Staff can see all orders
        else:
            # Regular users can only see their own orders
            return queryset.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Set user when creating order"""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=["get"])
    def items(self, request, pk=None):
        """Get all items in this order"""
        order = self.get_object()
        items = (
            OrderProduct.objects.filter(order=order)
            .select_related("product", "user", "payment")
            .prefetch_related("variation")
        )

        serializer = OrderProductListSerializer(
            items, many=True, context={"request": request}
        )
        return Response(serializer.data)

    @action(detail=True, methods=["get"])
    def summary(self, request, pk=None):
        """Get order summary with totals"""
        order = self.get_object()
        items = OrderProduct.objects.filter(order=order)

        total_items = items.count()
        total_quantity = sum(item.quantity for item in items)
        items_total = sum(item.quantity * item.product_price for item in items)

        return Response(
            {
                "order_number": order.order_number,
                "status": order.status,
                "total_items": total_items,
                "total_quantity": total_quantity,
                "items_total": items_total,
                "tax": order.tax,
                "grand_total": order.grand_total,
                "is_ordered": order.is_ordered,
                "created_at": order.created_at,
                "items": OrderProductListSerializer(
                    items, many=True, context={"request": request}
                ).data,
            }
        )

    @action(detail=True, methods=["post"])
    def update_status(self, request, pk=None):
        """Update order status"""
        order = self.get_object()
        new_status = request.data.get("status")

        if not new_status:
            return Response(
                {"error": "Status is required"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Validate status choice
        valid_statuses = [choice[0] for choice in Order.STATUS]
        if new_status not in valid_statuses:
            return Response(
                {"error": f"Invalid status. Valid choices are: {valid_statuses}"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        order.status = new_status
        order.save()

        serializer = self.get_serializer(order)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def mark_ordered(self, request, pk=None):
        """Mark order as ordered"""
        order = self.get_object()
        order.is_ordered = True
        order.save()

        # Also mark all order products as ordered
        OrderProduct.objects.filter(order=order).update(ordered=True)

        serializer = self.get_serializer(order)
        return Response(serializer.data)


class OrderProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for OrderProduct model
    Provides CRUD operations for order items
    """

    queryset = OrderProduct.objects.select_related(
        "product", "user", "order", "payment"
    ).prefetch_related("variation")
    serializer_class = OrderProductSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter, DjangoFilterBackend]
    search_fields = [
        "product__name",
        "order__order_number",
        "user__email",
        "user__first_name",
        "user__last_name",
    ]
    ordering_fields = ["created_at", "quantity", "product_price"]
    ordering = ["-created_at"]
    filterset_fields = [
        "ordered",
        "product__category",
        "order__status",
        "payment__payment_method",
        "user",
    ]

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == "list":
            return OrderProductListSerializer
        elif self.action == "create":
            return OrderProductCreateSerializer
        return OrderProductSerializer

    def get_queryset(self):
        """Filter order products based on user permissions"""
        queryset = self.queryset

        if self.request.user.is_staff:
            return queryset  # Staff can see all order products
        else:
            # Regular users can only see their own order products
            return queryset.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Set user when creating order product"""
        serializer.save(user=self.request.user)

    @action(detail=True, methods=["post"])
    def update_quantity(self, request, pk=None):
        """Update quantity of an order item"""
        order_product = self.get_object()
        quantity = request.data.get("quantity")

        if not quantity or not isinstance(quantity, int) or quantity < 1:
            return Response(
                {"error": "Valid quantity is required (minimum 1)"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        order_product.quantity = quantity
        order_product.save()

        serializer = self.get_serializer(order_product)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def update_price(self, request, pk=None):
        """Update product price of an order item"""
        order_product = self.get_object()
        product_price = request.data.get("product_price")

        if product_price is None or product_price < 0:
            return Response(
                {"error": "Valid product price is required (minimum 0)"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        order_product.product_price = product_price
        order_product.save()

        serializer = self.get_serializer(order_product)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def toggle_ordered(self, request, pk=None):
        """Toggle ordered status of order item"""
        order_product = self.get_object()
        order_product.ordered = not order_product.ordered
        order_product.save()

        serializer = self.get_serializer(order_product)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_order(self, request):
        """Get order products by order ID"""
        order_id = request.query_params.get("order_id")

        if not order_id:
            return Response(
                {"error": "order_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            order_id = int(order_id)
        except ValueError:
            return Response(
                {"error": "order_id must be a valid integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.get_queryset().filter(order_id=order_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def by_product(self, request):
        """Get order products by product ID"""
        product_id = request.query_params.get("product_id")

        if not product_id:
            return Response(
                {"error": "product_id parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            product_id = int(product_id)
        except ValueError:
            return Response(
                {"error": "product_id must be a valid integer"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        queryset = self.get_queryset().filter(product_id=product_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
