from rest_framework import serializers

from account.models import UserAccount
from orders.models import Order, OrderProduct, Payment
from products.models import Product, Variation


class PaymentSerializer(serializers.ModelSerializer):
    """Serializer for Payment model"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    payment_method_display = serializers.CharField(
        source="get_payment_method_display", read_only=True
    )

    class Meta:
        model = Payment
        fields = [
            "id",
            "user",
            "user_name",
            "payment_id",
            "payment_method",
            "payment_method_display",
        ]
        read_only_fields = ["id"]

    def validate_payment_id(self, value):
        """Validate payment_id uniqueness"""
        if Payment.objects.filter(payment_id=value).exists():
            raise serializers.ValidationError("Payment with this ID already exists")
        return value


class PaymentCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Payment"""

    class Meta:
        model = Payment
        fields = ["payment_id", "payment_method"]

    def validate_payment_id(self, value):
        """Validate payment_id uniqueness"""
        if Payment.objects.filter(payment_id=value).exists():
            raise serializers.ValidationError("Payment with this ID already exists")
        return value


class PaymentListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Payment list view"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    payment_method_display = serializers.CharField(
        source="get_payment_method_display", read_only=True
    )
    order_count = serializers.SerializerMethodField()

    class Meta:
        model = Payment
        fields = [
            "id",
            "payment_id",
            "payment_method_display",
            "user_name",
            "order_count",
        ]

    def get_order_count(self, obj):
        """Get count of orders using this payment"""
        return getattr(obj, "_order_count", obj.order_set.count())


# Nested serializers for related models
class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user info for orders"""

    full_name = serializers.CharField(source="get_full_name", read_only=True)

    class Meta:
        model = UserAccount
        fields = ["id", "email", "first_name", "last_name", "full_name"]


class ProductBasicSerializer(serializers.ModelSerializer):
    """Basic product info for order items"""

    category_name = serializers.CharField(source="category.name", read_only=True)

    class Meta:
        model = Product
        fields = ["id", "name", "price", "image", "category_name"]


class VariationSerializer(serializers.ModelSerializer):
    """Serializer for Variation model"""

    class Meta:
        model = Variation
        fields = ["id", "variation_category", "variation_value", "is_active"]


class OrderSerializer(serializers.ModelSerializer):
    """Serializer for Order model"""

    user = UserBasicSerializer(read_only=True)
    payment = PaymentSerializer(read_only=True)
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    full_name = serializers.CharField(read_only=True)
    order_items_count = serializers.SerializerMethodField()
    order_items = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            "id",
            "user",
            "payment",
            "order_number",
            "first_name",
            "last_name",
            "full_name",
            "phone",
            "email",
            "state",
            "area",
            "address",
            "status",
            "status_display",
            "ip",
            "grand_total",
            "tax",
            "is_ordered",
            "order_note",
            "created_at",
            "upated_at",
            "order_items_count",
            "order_items",
        ]
        read_only_fields = ["id", "created_at", "upated_at", "full_name"]

    def get_order_items_count(self, obj):
        """Get count of items in this order"""
        return obj.orderproduct_set.count()

    def get_order_items(self, obj):
        """Get order items for this order"""
        items = obj.orderproduct_set.select_related("product").prefetch_related(
            "variation"
        )
        return OrderProductListSerializer(items, many=True, context=self.context).data

    def validate_phone(self, value):
        """Validate phone number"""
        if value and len(value) != 10:
            raise serializers.ValidationError("Phone number must be exactly 10 digits")
        if value and not value.isdigit():
            raise serializers.ValidationError("Phone number must contain only digits")
        return value

    def validate_grand_total(self, value):
        """Validate grand total is positive"""
        if value < 0:
            raise serializers.ValidationError("Grand total cannot be negative")
        return value


class OrderCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating Order"""

    payment_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = Order
        fields = [
            "order_number",
            "first_name",
            "last_name",
            "phone",
            "email",
            "state",
            "area",
            "address",
            "grand_total",
            "tax",
            "order_note",
            "payment_id",
        ]

    def validate_order_number(self, value):
        """Validate order number uniqueness"""
        if Order.objects.filter(order_number=value).exists():
            raise serializers.ValidationError("Order with this number already exists")
        return value

    def validate_payment_id(self, value):
        """Validate payment exists"""
        if value:
            try:
                Payment.objects.get(id=value)
                return value
            except Payment.DoesNotExist:
                raise serializers.ValidationError("Payment does not exist")
        return value

    def create(self, validated_data):
        """Create order with payment"""
        payment_id = validated_data.pop("payment_id", None)
        payment = None

        if payment_id:
            payment = Payment.objects.get(id=payment_id)

        # Set user if authenticated
        user = (
            self.context["request"].user
            if self.context["request"].user.is_authenticated
            else None
        )

        order = Order.objects.create(user=user, payment=payment, **validated_data)

        return order


class OrderListSerializer(serializers.ModelSerializer):
    """Optimized serializer for Order list view"""

    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    payment_method = serializers.CharField(
        source="payment.payment_method", read_only=True
    )
    status_display = serializers.CharField(source="get_status_display", read_only=True)
    full_name = serializers.CharField(read_only=True)
    order_items_count = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            "id",
            "order_number",
            "user_name",
            "full_name",
            "email",
            "status",
            "status_display",
            "payment_method",
            "grand_total",
            "is_ordered",
            "order_items_count",
            "created_at",
        ]

    def get_order_items_count(self, obj):
        """Get count of items in this order"""
        return getattr(obj, "_items_count", obj.orderproduct_set.count())


class OrderProductSerializer(serializers.ModelSerializer):
    """Serializer for OrderProduct model"""

    user = UserBasicSerializer(read_only=True)
    payment = PaymentSerializer(read_only=True)
    product = ProductBasicSerializer(read_only=True)
    variation = VariationSerializer(many=True, read_only=True)
    order_number = serializers.CharField(source="order.order_number", read_only=True)
    subtotal = serializers.SerializerMethodField()

    class Meta:
        model = OrderProduct
        fields = [
            "id",
            "user",
            "payment",
            "order",
            "order_number",
            "product",
            "variation",
            "quantity",
            "product_price",
            "subtotal",
            "ordered",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_subtotal(self, obj):
        """Calculate subtotal for this order item"""
        return obj.quantity * obj.product_price

    def validate_quantity(self, value):
        """Validate quantity is positive"""
        if value < 1:
            raise serializers.ValidationError("Quantity must be at least 1")
        return value

    def validate_product_price(self, value):
        """Validate product price is positive"""
        if value < 0:
            raise serializers.ValidationError("Product price cannot be negative")
        return value


class OrderProductCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating OrderProduct"""

    product_id = serializers.IntegerField(write_only=True)
    order_id = serializers.IntegerField(write_only=True)
    payment_id = serializers.IntegerField(write_only=True, required=False)
    variation_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        allow_empty=True,
    )

    class Meta:
        model = OrderProduct
        fields = [
            "product_id",
            "order_id",
            "payment_id",
            "variation_ids",
            "quantity",
            "product_price",
        ]

    def validate_product_id(self, value):
        """Validate product exists"""
        try:
            Product.objects.get(id=value)
            return value
        except Product.DoesNotExist:
            raise serializers.ValidationError("Product does not exist")

    def validate_order_id(self, value):
        """Validate order exists"""
        try:
            Order.objects.get(id=value)
            return value
        except Order.DoesNotExist:
            raise serializers.ValidationError("Order does not exist")

    def validate_payment_id(self, value):
        """Validate payment exists"""
        if value:
            try:
                Payment.objects.get(id=value)
                return value
            except Payment.DoesNotExist:
                raise serializers.ValidationError("Payment does not exist")
        return value

    def validate_variation_ids(self, value):
        """Validate variations exist and are active"""
        if value:
            variations = Variation.objects.filter(id__in=value)
            if len(variations) != len(value):
                raise serializers.ValidationError("One or more variations do not exist")

            # Check if all variations are active
            inactive_variations = variations.filter(is_active=False)
            if inactive_variations.exists():
                raise serializers.ValidationError(
                    "One or more variations are not active"
                )

        return value

    def validate(self, attrs):
        """Cross-field validation"""
        product_id = attrs.get("product_id")
        variation_ids = attrs.get("variation_ids", [])

        if variation_ids:
            # Ensure all variations belong to the specified product
            product = Product.objects.get(id=product_id)
            variations = Variation.objects.filter(id__in=variation_ids)

            for variation in variations:
                if variation.product != product:
                    raise serializers.ValidationError(
                        f"Variation '{
                            variation.variation_value
                        }' does not belong to product '{product.name}'"
                    )

        return attrs

    def create(self, validated_data):
        """Create order product with variations"""
        product_id = validated_data.pop("product_id")
        order_id = validated_data.pop("order_id")
        payment_id = validated_data.pop("payment_id", None)
        variation_ids = validated_data.pop("variation_ids", [])

        product = Product.objects.get(id=product_id)
        order = Order.objects.get(id=order_id)
        payment = Payment.objects.get(id=payment_id) if payment_id else None

        # Set user from request context
        user = (
            self.context["request"].user
            if self.context["request"].user.is_authenticated
            else None
        )

        order_product = OrderProduct.objects.create(
            user=user, product=product, order=order, payment=payment, **validated_data
        )

        if variation_ids:
            variations = Variation.objects.filter(id__in=variation_ids)
            order_product.variation.set(variations)

        return order_product


class OrderProductListSerializer(serializers.ModelSerializer):
    """Optimized serializer for OrderProduct list view"""

    product_name = serializers.CharField(source="product.name", read_only=True)
    product_image = serializers.ImageField(source="product.image", read_only=True)
    user_name = serializers.CharField(source="user.get_full_name", read_only=True)
    order_number = serializers.CharField(source="order.order_number", read_only=True)
    payment_method = serializers.CharField(
        source="payment.payment_method", read_only=True
    )
    subtotal = serializers.SerializerMethodField()
    variations_display = serializers.SerializerMethodField()

    class Meta:
        model = OrderProduct
        fields = [
            "id",
            "product_name",
            "product_image",
            "user_name",
            "order_number",
            "payment_method",
            "quantity",
            "product_price",
            "subtotal",
            "variations_display",
            "ordered",
            "created_at",
        ]

    def get_subtotal(self, obj):
        """Calculate subtotal for this order item"""
        return obj.quantity * obj.product_price

    def get_variations_display(self, obj):
        """Get formatted variations display"""
        variations = obj.variation.filter(is_active=True)
        if variations.exists():
            return [f"{v.variation_category}: {v.variation_value}" for v in variations]
        return []
