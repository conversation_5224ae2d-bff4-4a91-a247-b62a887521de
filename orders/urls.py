from django.urls import include, path
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework.routers import DefaultRouter

from orders import views

# DRF Router
router = DefaultRouter()
router.register(r"payments", views.PaymentViewSet, basename="payment")
router.register(r"orders", views.OrderViewSet, basename="order")
router.register(r"order-products", views.OrderProductViewSet, basename="orderproduct")


@api_view(["GET"])
def orders_api_root(request, format=None):
    """
    API root for orders app
    """
    return Response(
        {
            "payments": reverse("payment-list", request=request, format=format),
            "orders": reverse("order-list", request=request, format=format),
            "order-products": reverse(
                "orderproduct-list", request=request, format=format
            ),
        }
    )


urlpatterns = [
    path("", orders_api_root, name="orders-api-root"),
    path("", include(router.urls)),
]
