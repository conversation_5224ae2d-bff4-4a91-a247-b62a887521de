from django.contrib import admin
from django.contrib.admin import Simple<PERSON>istFilter
from django.db.models import Count, F, Sum
from django.urls import reverse
from django.utils import timezone
from django.utils.html import format_html

from carts.models import Cart, CartItem


class CartItemCountFilter(SimpleListFilter):
    title = "Cart Item Count"
    parameter_name = "item_count"

    def lookups(self, request, model_admin):
        return (
            ("empty", "Empty Carts"),
            ("1-3", "1-3 Items"),
            ("4-10", "4-10 Items"),
            ("10+", "More than 10 Items"),
        )

    def queryset(self, request, queryset):
        if self.value() == "empty":
            return queryset.annotate(item_count=Count("cartitem")).filter(item_count=0)
        elif self.value() == "1-3":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__range=(1, 3)
            )
        elif self.value() == "4-10":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__range=(4, 10)
            )
        elif self.value() == "10+":
            return queryset.annotate(item_count=Count("cartitem")).filter(
                item_count__gt=10
            )


class CartAgeFilter(SimpleListFilter):
    title = "Cart Age"
    parameter_name = "cart_age"

    def lookups(self, request, model_admin):
        return (
            ("today", "Today"),
            ("week", "This Week"),
            ("month", "This Month"),
            ("old", "Older than 1 Month"),
        )

    def queryset(self, request, queryset):
        now = timezone.now()
        if self.value() == "today":
            return queryset.filter(date_added__date=now.date())
        elif self.value() == "week":
            week_ago = now - timezone.timedelta(days=7)
            return queryset.filter(date_added__gte=week_ago)
        elif self.value() == "month":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(date_added__gte=month_ago)
        elif self.value() == "old":
            month_ago = now - timezone.timedelta(days=30)
            return queryset.filter(date_added__lt=month_ago)


@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = (
        "cart_id_display",
        "item_count",
        "total_value",
        "date_added",
        "cart_age",
    )
    search_fields = ("cart_id",)
    ordering = ("-date_added",)
    list_filter = (CartItemCountFilter, CartAgeFilter, "date_added")
    readonly_fields = ("date_added", "cart_summary")
    list_per_page = 10
    verbose_name = "Shopping Cart"
    verbose_name_plural = "Shopping Carts"
    date_hierarchy = "date_added"

    fieldsets = (
        ("Cart Information", {"fields": ("cart_id", "date_added", "cart_summary")}),
    )

    actions = ["delete_empty_carts", "delete_old_carts"]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _item_count=Count("cartitem"),
            _total_value=Sum(F("cartitem__product__price") * F("cartitem__quantity")),
        ).prefetch_related("cartitem_set__product", "cartitem_set__user")
        return queryset

    def cart_id_display(self, obj):
        return format_html(
            "<code>{}</code>",
            obj.cart_id[:20] + "..." if len(obj.cart_id) > 20 else obj.cart_id,
        )

    cart_id_display.short_description = "Cart ID"
    cart_id_display.admin_order_field = "cart_id"

    def item_count(self, obj):
        count = obj._item_count
        if count > 0:
            url = (
                reverse("admin:carts_cartitem_changelist")
                + f"?cart__id__exact={obj.id}"
            )
            return format_html('<a href="{}">{} items</a>', url, count)
        return format_html('<span style="color: #999;">Empty</span>')

    item_count.short_description = "Items"
    item_count.admin_order_field = "_item_count"

    def total_value(self, obj):
        total = obj._total_value or 0
        if total > 0:
            total_formatted = f"${total:.2f}"
            return format_html("<strong>{}</strong>", total_formatted)
        return format_html('<span style="color: #999;">$0.00</span>')

    total_value.short_description = "Total Value"
    total_value.admin_order_field = "_total_value"

    def cart_age(self, obj):
        now = timezone.now()
        age = now - obj.date_added
        if age.days == 0:
            hours = age.seconds // 3600
            return format_html('<span style="color: green;">{} hours ago</span>', hours)
        elif age.days <= 7:
            return format_html(
                '<span style="color: orange;">{} days ago</span>', age.days
            )
        else:
            return format_html('<span style="color: red;">{} days ago</span>', age.days)

    cart_age.short_description = "Age"

    def cart_summary(self, obj):
        items = obj.cartitem_set.all()
        if not items:
            return format_html('<div style="color: #999;">Empty cart</div>')

        summary_html = '<div style="max-width: 500px;">'
        for item in items[:5]:  # Show first 5 items
            # Format the subtotal separately to avoid format_html issues
            subtotal_formatted = f"${item.sub_total():.2f}"
            summary_html += f"""
                <div style="padding: 5px; border-bottom: 1px solid #eee;">
                    <strong>{item.product.name}</strong>
                    <span style="color: #666;">x{item.quantity}</span>
                    <span style="float: right;">{subtotal_formatted}</span>
                </div>
            """
        if items.count() > 5:
            summary_html += f'<div style="padding: 5px; color: #999;">... and {
                items.count() - 5
            } more items</div>'
        summary_html += "</div>"
        return format_html(summary_html)

    cart_summary.short_description = "Cart Summary"

    def delete_empty_carts(self, request, queryset):
        empty_carts = queryset.annotate(item_count=Count("cartitem")).filter(
            item_count=0
        )
        deleted_count = empty_carts.count()
        empty_carts.delete()
        self.message_user(request, f"{deleted_count} empty carts deleted.")

    delete_empty_carts.short_description = "Delete empty carts"

    def delete_old_carts(self, request, queryset):
        month_ago = timezone.now() - timezone.timedelta(days=30)
        old_carts = queryset.filter(date_added__lt=month_ago)
        deleted_count = old_carts.count()
        old_carts.delete()
        self.message_user(request, f"{deleted_count} old carts (>30 days) deleted.")

    delete_old_carts.short_description = "Delete old carts (older than 30 days)"


class UserFilter(SimpleListFilter):
    title = "User Type"
    parameter_name = "user_type"

    def lookups(self, request, model_admin):
        return (
            ("registered", "Registered Users"),
            ("anonymous", "Anonymous Users"),
        )

    def queryset(self, request, queryset):
        if self.value() == "registered":
            return queryset.filter(user__isnull=False)
        elif self.value() == "anonymous":
            return queryset.filter(user__isnull=True)


class QuantityFilter(SimpleListFilter):
    title = "Quantity Range"
    parameter_name = "quantity_range"

    def lookups(self, request, model_admin):
        return (
            ("1", "Single Item"),
            ("2-5", "2-5 Items"),
            ("6-10", "6-10 Items"),
            ("10+", "More than 10"),
        )

    def queryset(self, request, queryset):
        if self.value() == "1":
            return queryset.filter(quantity=1)
        elif self.value() == "2-5":
            return queryset.filter(quantity__range=(2, 5))
        elif self.value() == "6-10":
            return queryset.filter(quantity__range=(6, 10))
        elif self.value() == "10+":
            return queryset.filter(quantity__gt=10)


@admin.register(CartItem)
class CartItemAdmin(admin.ModelAdmin):
    list_display = (
        "product_link",
        "user_display",
        "cart_link",
        "quantity_display",
        "subtotal_display",
        "variations_display",
        "status_display",
        "is_active",
    )
    list_filter = ("is_active", UserFilter, QuantityFilter, "product__category")
    search_fields = (
        "product__name",
        "user__email",
        "user__first_name",
        "user__last_name",
        "cart__cart_id",
    )
    list_editable = ("is_active",)
    readonly_fields = ("subtotal_display", "variations_summary")
    ordering = ("-cart__date_added",)
    list_per_page = 15
    verbose_name = "Cart Item"
    verbose_name_plural = "Cart Items"

    fieldsets = (
        (
            "Item Information",
            {"fields": ("product", "user", "cart", "quantity", "subtotal_display")},
        ),
        (
            "Variations",
            {"fields": ("variation", "variations_summary"), "classes": ("collapse",)},
        ),
        ("Status", {"fields": ("is_active",)}),
    )

    filter_horizontal = ("variation",)
    actions = ["activate_items", "deactivate_items", "remove_from_cart"]

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related("product", "user", "cart")
            .prefetch_related("variation")
        )

    def product_link(self, obj):
        url = reverse("admin:products_product_change", args=[obj.product.pk])
        return format_html('<a href="{}">{}</a>', url, obj.product.name)

    product_link.short_description = "Product"
    product_link.admin_order_field = "product__name"

    def user_display(self, obj):
        if obj.user:
            display_name = (
                f"{obj.user.first_name} {obj.user.last_name}".strip() or obj.user.email
            )
            url = reverse("admin:account_useraccount_change", args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, display_name)
        return format_html('<span style="color: #999;">Anonymous</span>')

    user_display.short_description = "User"
    user_display.admin_order_field = "user__email"

    def cart_link(self, obj):
        if obj.cart:
            url = reverse("admin:carts_cart_change", args=[obj.cart.pk])
            cart_display = (
                obj.cart.cart_id[:15] + "..."
                if len(obj.cart.cart_id) > 15
                else obj.cart.cart_id
            )
            return format_html('<a href="{}"><code>{}</code></a>', url, cart_display)
        return format_html('<span style="color: #999;">No Cart</span>')

    cart_link.short_description = "Cart"
    cart_link.admin_order_field = "cart__cart_id"

    def quantity_display(self, obj):
        if obj.quantity > 5:
            return format_html(
                '<span style="color: orange; font-weight: bold;">{}</span>',
                obj.quantity,
            )
        return str(obj.quantity)

    quantity_display.short_description = "Qty"
    quantity_display.admin_order_field = "quantity"

    def subtotal_display(self, obj):
        subtotal = obj.sub_total()
        subtotal_formatted = f"${subtotal:.2f}"
        return format_html("<strong>{}</strong>", subtotal_formatted)

    subtotal_display.short_description = "Subtotal"

    def variations_display(self, obj):
        variations = obj.variation.all()
        if variations:
            var_list = [
                f"{v.variation_category}: {v.variation_value}" for v in variations[:2]
            ]
            display = ", ".join(var_list)
            if variations.count() > 2:
                display += f" (+{variations.count() - 2} more)"
            return format_html(
                '<span style="font-size: 11px; color: #666;">{}</span>', display
            )
        return format_html('<span style="color: #999;">No variations</span>')

    variations_display.short_description = "Variations"

    def variations_summary(self, obj):
        variations = obj.variation.all()
        if not variations:
            return format_html('<div style="color: #999;">No variations selected</div>')

        summary_html = '<div style="max-width: 400px;">'
        for variation in variations:
            summary_html += f"""
                <div style="padding: 3px; border-bottom: 1px solid #eee;">
                    <strong>{variation.variation_category}:</strong> {variation.variation_value}
                </div>
            """
        summary_html += "</div>"
        return format_html(summary_html)

    variations_summary.short_description = "Variation Details"

    def status_display(self, obj):
        if obj.is_active:
            return format_html('<span style="color: green;">✓ Active</span>')
        return format_html('<span style="color: red;">✗ Inactive</span>')

    status_display.short_description = "Status"
    status_display.admin_order_field = "is_active"

    def activate_items(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f"{updated} cart items activated.")

    activate_items.short_description = "Activate selected cart items"

    def deactivate_items(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f"{updated} cart items deactivated.")

    deactivate_items.short_description = "Deactivate selected cart items"

    def remove_from_cart(self, request, queryset):
        deleted_count = queryset.count()
        queryset.delete()
        self.message_user(request, f"{deleted_count} cart items removed.")

    remove_from_cart.short_description = "Remove selected items from cart"
