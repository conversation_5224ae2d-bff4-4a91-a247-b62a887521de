#!/usr/bin/env python
"""
Comprehensive fake data generation script for MahBeauty Django project.
This script generates realistic test data for all models using Faker.

Usage:
    python generate_fake_data.py

Requirements:
    pip install faker pillow
"""

import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta
import random
import uuid
from io import BytesIO
from PIL import Image

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mahbeauty.settings')
django.setup()

from faker import Faker
from django.utils import timezone
from django.core.files.base import ContentFile

# Import all models
from account.models import UserAccount
from products.models import ProductCategory, Product, Variation, ReviewRating
from orders.models import Payment, Order, OrderProduct
from carts.models import Cart, CartItem
from store.models import Organization, HeroSlide, HeroSlideImage, AboutPageContent, Testimonial

fake = Faker()

# Beauty-specific data
BEAUTY_CATEGORIES = [
    'Skincare', 'Makeup', 'Haircare', 'Fragrance', 'Body Care', 
    'Nail Care', 'Men\'s Grooming', 'Tools & Accessories'
]

BEAUTY_PRODUCTS = {
    'Skincare': [
        'Vitamin C Serum', 'Hyaluronic Acid Moisturizer', 'Retinol Night Cream',
        'Gentle Cleanser', 'Exfoliating Toner', 'Sunscreen SPF 50', 'Eye Cream',
        'Face Mask', 'Micellar Water', 'Anti-Aging Serum'
    ],
    'Makeup': [
        'Foundation', 'Concealer', 'Lipstick', 'Mascara', 'Eyeshadow Palette',
        'Blush', 'Bronzer', 'Eyeliner', 'Lip Gloss', 'Setting Powder'
    ],
    'Haircare': [
        'Shampoo', 'Conditioner', 'Hair Mask', 'Hair Oil', 'Leave-in Treatment',
        'Dry Shampoo', 'Hair Serum', 'Styling Gel', 'Heat Protectant', 'Hair Spray'
    ],
    'Fragrance': [
        'Eau de Parfum', 'Eau de Toilette', 'Body Mist', 'Perfume Oil',
        'Cologne', 'Scented Candle', 'Room Spray', 'Car Freshener'
    ],
    'Body Care': [
        'Body Lotion', 'Body Wash', 'Body Scrub', 'Hand Cream',
        'Foot Cream', 'Deodorant', 'Body Oil', 'Bath Bomb'
    ],
    'Nail Care': [
        'Nail Polish', 'Base Coat', 'Top Coat', 'Cuticle Oil',
        'Nail Strengthener', 'Nail File', 'Nail Buffer', 'Nail Art Kit'
    ],
    'Men\'s Grooming': [
        'Beard Oil', 'Aftershave', 'Face Wash', 'Shaving Cream',
        'Hair Pomade', 'Body Wash', 'Cologne', 'Moisturizer'
    ],
    'Tools & Accessories': [
        'Makeup Brushes', 'Beauty Sponge', 'Hair Brush', 'Tweezers',
        'Eyelash Curler', 'Makeup Mirror', 'Hair Dryer', 'Straightener'
    ]
}

BEAUTY_INGREDIENTS = [
    'Hyaluronic Acid', 'Vitamin C', 'Retinol', 'Niacinamide', 'Salicylic Acid',
    'Glycolic Acid', 'Peptides', 'Ceramides', 'Argan Oil', 'Jojoba Oil',
    'Aloe Vera', 'Green Tea Extract', 'Vitamin E', 'Collagen', 'Zinc Oxide'
]

BEAUTY_BENEFITS = [
    'Hydrates and moisturizes skin', 'Reduces fine lines and wrinkles',
    'Brightens complexion', 'Evens skin tone', 'Provides sun protection',
    'Strengthens hair', 'Adds volume and shine', 'Long-lasting wear',
    'Waterproof formula', 'Suitable for sensitive skin', 'Anti-aging properties',
    'Natural ingredients', 'Cruelty-free', 'Vegan formula', 'Dermatologist tested'
]

COLOR_VARIATIONS = [
    'Fair', 'Light', 'Medium', 'Tan', 'Deep', 'Red', 'Pink', 'Coral',
    'Berry', 'Nude', 'Brown', 'Black', 'Blue', 'Green', 'Purple', 'Gold'
]

NEPAL_STATES = [
    'Bagmati', 'Gandaki', 'Lumbini', 'Karnali', 'Sudurpashchim', 'Koshi', 'Madhesh'
]

NEPAL_AREAS = [
    'Kathmandu', 'Lalitpur', 'Bhaktapur', 'Pokhara', 'Chitwan', 'Butwal',
    'Biratnagar', 'Janakpur', 'Nepalgunj', 'Dharan', 'Hetauda', 'Itahari'
]

def create_fake_image(width=400, height=400, format='JPEG'):
    """Create a fake image for testing purposes"""
    image = Image.new('RGB', (width, height), color=fake.color())
    image_io = BytesIO()
    image.save(image_io, format=format)
    image_io.seek(0)
    return ContentFile(image_io.getvalue(), name=f'{fake.uuid4()}.jpg')

def generate_users(count=50):
    """Generate fake users"""
    print(f"Generating {count} users...")
    users = []
    
    for i in range(count):
        # Create some staff users
        is_staff = i < 5
        is_superuser = i < 2
        
        user = UserAccount.objects.create_user(
            email=fake.unique.email(),
            password='testpass123',
            first_name=fake.first_name(),
            last_name=fake.last_name(),
            google_id=fake.uuid4() if random.choice([True, False]) else None,
            is_active=random.choice([True, True, True, False]),  # Mostly active
            is_staff=is_staff,
            is_superuser=is_superuser
        )
        users.append(user)
    
    print(f"Created {len(users)} users")
    return users

def generate_categories():
    """Generate product categories"""
    print("Generating product categories...")
    categories = []
    
    for category_name in BEAUTY_CATEGORIES:
        category, created = ProductCategory.objects.get_or_create(
            name=category_name
        )
        categories.append(category)
    
    print(f"Created {len(categories)} categories")
    return categories

def generate_products(categories, count=200):
    """Generate fake products"""
    print(f"Generating {count} products...")
    products = []
    
    for i in range(count):
        category = random.choice(categories)
        product_names = BEAUTY_PRODUCTS.get(category.name, ['Beauty Product'])
        base_name = random.choice(product_names)
        
        # Add brand variation
        brand = fake.company()
        name = f"{brand} {base_name}"
        
        # Generate price
        base_price = Decimal(str(random.uniform(10.00, 500.00))).quantize(Decimal('0.01'))
        
        # Generate ingredients
        ingredients = []
        for _ in range(random.randint(3, 8)):
            ingredients.append({
                'name': random.choice(BEAUTY_INGREDIENTS),
                'quantity': f"{random.randint(1, 20)}%"
            })
        
        # Generate benefits
        benefits = random.sample(BEAUTY_BENEFITS, random.randint(2, 5))
        
        # Offer settings
        is_on_offer = random.choice([True, False, False])  # 33% chance
        offer_price = None
        offer_start_date = None
        offer_end_date = None
        
        if is_on_offer:
            discount = random.uniform(0.1, 0.5)  # 10-50% discount
            offer_price = (base_price * Decimal(str(1 - discount))).quantize(Decimal('0.01'))
            offer_start_date = timezone.now() - timedelta(days=random.randint(0, 30))
            offer_end_date = offer_start_date + timedelta(days=random.randint(7, 60))
        
        product = Product.objects.create(
            name=name,
            description=fake.text(max_nb_chars=500),
            price=base_price,
            category=category,
            rating=round(random.uniform(0, 5), 1),
            in_stock=random.choice([True, True, True, False]),  # Mostly in stock
            ingredients=ingredients,
            usage_instructions=fake.text(max_nb_chars=300),
            benefits=benefits,
            image=create_fake_image() if random.choice([True, False]) else None,
            is_active=random.choice([True, True, True, False]),  # Mostly active
            is_on_offer=is_on_offer,
            offer_price=offer_price,
            offer_start_date=offer_start_date,
            offer_end_date=offer_end_date,
            offer_is_active=is_on_offer and random.choice([True, False])
        )
        products.append(product)
    
    print(f"Created {len(products)} products")
    return products

def generate_variations(products, count=300):
    """Generate product variations"""
    print(f"Generating {count} variations...")
    variations = []
    
    for i in range(count):
        product = random.choice(products)
        
        variation = Variation.objects.create(
            product=product,
            variation_category='color',
            variation_value=random.choice(COLOR_VARIATIONS),
            is_active=random.choice([True, True, False])  # Mostly active
        )
        variations.append(variation)
    
    print(f"Created {len(variations)} variations")
    return variations

def generate_reviews(users, products, count=500):
    """Generate product reviews"""
    print(f"Generating {count} reviews...")
    reviews = []

    for i in range(count):
        user = random.choice(users)
        product = random.choice(products)

        # Avoid duplicate reviews from same user for same product
        if ReviewRating.objects.filter(user=user, product=product).exists():
            continue

        rating = random.choice([1, 2, 3, 4, 4, 4, 5, 5, 5, 5])  # Skewed towards higher ratings

        review = ReviewRating.objects.create(
            product=product,
            user=user,
            subject=fake.sentence(nb_words=6),
            review=fake.text(max_nb_chars=300),
            rating=rating,
            ip=fake.ipv4()
        )
        reviews.append(review)

    print(f"Created {len(reviews)} reviews")
    return reviews

def generate_payments(users, count=100):
    """Generate payment records"""
    print(f"Generating {count} payments...")
    payments = []

    payment_methods = ["Cash on Delivery", "ESewa", "Khalti", "Bank Transfer", "Fonepay"]

    for i in range(count):
        user = random.choice(users)
        method = random.choice(payment_methods)

        # Generate payment ID based on method
        if method == "Cash on Delivery":
            payment_id = f"COD-{fake.uuid4()[:8]}"
        elif method == "ESewa":
            payment_id = f"ESW-{fake.random_number(digits=10)}"
        elif method == "Khalti":
            payment_id = f"KHT-{fake.random_number(digits=10)}"
        elif method == "Bank Transfer":
            payment_id = f"BNK-{fake.random_number(digits=12)}"
        else:  # Fonepay
            payment_id = f"FNP-{fake.random_number(digits=10)}"

        payment = Payment.objects.create(
            user=user,
            payment_id=payment_id,
            payment_method=method
        )
        payments.append(payment)

    print(f"Created {len(payments)} payments")
    return payments

def generate_orders(users, payments, count=150):
    """Generate orders"""
    print(f"Generating {count} orders...")
    orders = []

    statuses = ["Processing", "Packed", "Shipped", "Delivered", "Cancel"]

    for i in range(count):
        user = random.choice(users)
        payment = random.choice(payments) if random.choice([True, False]) else None

        # Generate order number
        order_number = f"ORD-{timezone.now().year}-{fake.random_number(digits=6)}"

        # Use user's name or generate random names
        if random.choice([True, False]) and user:
            first_name = user.first_name
            last_name = user.last_name
            email = user.email
        else:
            first_name = fake.first_name()
            last_name = fake.last_name()
            email = fake.email()

        # Generate totals
        grand_total = random.randint(500, 15000)  # NPR
        tax = round(grand_total * 0.13, 2)  # 13% VAT in Nepal

        order = Order.objects.create(
            user=user,
            payment=payment,
            order_number=order_number,
            first_name=first_name,
            last_name=last_name,
            phone=f"98{fake.random_number(digits=8)}",  # Nepal mobile format
            email=email,
            state=random.choice(NEPAL_STATES),
            area=random.choice(NEPAL_AREAS),
            address=fake.address(),
            status=random.choice(statuses),
            ip=fake.ipv4(),
            grand_total=grand_total,
            tax=tax,
            is_ordered=random.choice([True, True, False]),  # Mostly ordered
            order_note=fake.text(max_nb_chars=200) if random.choice([True, False]) else ""
        )
        orders.append(order)

    print(f"Created {len(orders)} orders")
    return orders

def generate_order_products(users, orders, products, variations, count=400):
    """Generate order products"""
    print(f"Generating {count} order products...")
    order_products = []

    for i in range(count):
        user = random.choice(users)
        order = random.choice(orders)
        product = random.choice(products)

        # Get payment from order
        payment = order.payment

        quantity = random.randint(1, 5)
        product_price = int(product.price * 100)  # Convert to paisa/cents

        order_product = OrderProduct.objects.create(
            user=user,
            payment=payment,
            order=order,
            product=product,
            quantity=quantity,
            product_price=product_price,
            ordered=random.choice([True, True, False])  # Mostly ordered
        )

        # Add variations
        product_variations = [v for v in variations if v.product == product]
        if product_variations:
            selected_variations = random.sample(
                product_variations,
                min(random.randint(0, 2), len(product_variations))
            )
            order_product.variation.set(selected_variations)

        order_products.append(order_product)

    print(f"Created {len(order_products)} order products")
    return order_products

def generate_carts(count=30):
    """Generate shopping carts"""
    print(f"Generating {count} carts...")
    carts = []

    for i in range(count):
        cart = Cart.objects.create(
            cart_id=fake.uuid4()
        )
        carts.append(cart)

    print(f"Created {len(carts)} carts")
    return carts

def generate_cart_items(users, carts, products, variations, count=100):
    """Generate cart items"""
    print(f"Generating {count} cart items...")
    cart_items = []

    for i in range(count):
        user = random.choice(users) if random.choice([True, False]) else None
        cart = random.choice(carts)
        product = random.choice(products)

        cart_item = CartItem.objects.create(
            user=user,
            product=product,
            cart=cart,
            quantity=random.randint(1, 5),
            is_active=random.choice([True, True, False])  # Mostly active
        )

        # Add variations
        product_variations = [v for v in variations if v.product == product]
        if product_variations:
            selected_variations = random.sample(
                product_variations,
                min(random.randint(0, 2), len(product_variations))
            )
            cart_item.variation.set(selected_variations)

        cart_items.append(cart_item)

    print(f"Created {len(cart_items)} cart items")
    return cart_items

def generate_organization():
    """Generate organization data"""
    print("Generating organization...")

    # Create or update organization
    org, created = Organization.objects.get_or_create(
        name="MahBeauty",
        defaults={
            'logo': create_fake_image(200, 200),
            'location': fake.address(),
            'phone': f"98{fake.random_number(digits=8)}",
            'email': fake.company_email(),
            'mapping_url': fake.url(),
            'instagram': f"https://instagram.com/{fake.user_name()}",
            'facebook': f"https://facebook.com/{fake.user_name()}",
            'youtube': f"https://youtube.com/c/{fake.user_name()}",
            'tiktok': f"https://tiktok.com/@{fake.user_name()}"
        }
    )

    print(f"{'Created' if created else 'Updated'} organization: {org.name}")
    return org

def generate_hero_slides(count=5):
    """Generate hero slides"""
    print(f"Generating {count} hero slides...")
    slides = []

    for i in range(count):
        slide = HeroSlide.objects.create(
            title=fake.catch_phrase(),
            description=fake.text(max_nb_chars=200),
            client=random.randint(100, 10000),
            service=random.randint(10, 100),
            experience=random.randint(1, 20)
        )

        # Add images to slide
        for j in range(random.randint(1, 3)):
            HeroSlideImage.objects.create(
                slide=slide,
                image=create_fake_image(800, 400),
                order=j
            )

        slides.append(slide)

    print(f"Created {len(slides)} hero slides")
    return slides

def generate_about_content():
    """Generate about page content"""
    print("Generating about page content...")
    content_types = ["story", "mission", "vision", "values", "header"]
    contents = []

    for content_type in content_types:
        content, created = AboutPageContent.objects.get_or_create(
            content_type=content_type,
            defaults={
                'title': f"Our {content_type.title()}",
                'content': fake.text(max_nb_chars=500),
                'subtitle': fake.sentence(),
                'image': create_fake_image(600, 400) if random.choice([True, False]) else None,
                'is_active': True
            }
        )
        contents.append(content)

    print(f"Created {len(contents)} about page contents")
    return contents

def generate_testimonials(users, count=50):
    """Generate testimonials"""
    print(f"Generating {count} testimonials...")
    testimonials = []

    for i in range(count):
        user = random.choice(users)

        testimonial = Testimonial.objects.create(
            user=user,
            is_featured=random.choice([True, False, False, False]),  # Few featured
            rating=random.choice([3, 4, 4, 5, 5, 5])  # Skewed towards higher ratings
        )
        testimonials.append(testimonial)

    print(f"Created {len(testimonials)} testimonials")
    return testimonials

def clear_existing_data():
    """Clear existing data (optional)"""
    print("Clearing existing data...")

    # Clear in reverse dependency order
    CartItem.objects.all().delete()
    Cart.objects.all().delete()
    OrderProduct.objects.all().delete()
    Order.objects.all().delete()
    Payment.objects.all().delete()
    ReviewRating.objects.all().delete()
    Testimonial.objects.all().delete()
    Variation.objects.all().delete()
    Product.objects.all().delete()
    ProductCategory.objects.all().delete()
    HeroSlideImage.objects.all().delete()
    HeroSlide.objects.all().delete()
    AboutPageContent.objects.all().delete()
    Organization.objects.all().delete()
    UserAccount.objects.filter(is_superuser=False).delete()  # Keep superusers

    print("Existing data cleared!")

def main():
    """Main function to generate all fake data"""
    print("=" * 60)
    print("MahBeauty Fake Data Generator")
    print("=" * 60)

    # Ask user if they want to clear existing data
    clear_data = input("Do you want to clear existing data? (y/N): ").lower().strip()
    if clear_data in ['y', 'yes']:
        clear_existing_data()

    print("\nStarting data generation...")
    start_time = timezone.now()

    try:
        # Generate data in dependency order
        users = generate_users(50)
        categories = generate_categories()
        products = generate_products(categories, 200)
        variations = generate_variations(products, 300)
        reviews = generate_reviews(users, products, 500)
        payments = generate_payments(users, 100)
        orders = generate_orders(users, payments, 150)
        order_products = generate_order_products(users, orders, products, variations, 400)
        carts = generate_carts(30)
        cart_items = generate_cart_items(users, carts, products, variations, 100)

        # Store data
        organization = generate_organization()
        hero_slides = generate_hero_slides(5)
        about_contents = generate_about_content()
        testimonials = generate_testimonials(users, 50)

        end_time = timezone.now()
        duration = end_time - start_time

        print("\n" + "=" * 60)
        print("DATA GENERATION COMPLETE!")
        print("=" * 60)
        print(f"Total time taken: {duration}")
        print("\nSummary:")
        print(f"  Users: {len(users)}")
        print(f"  Categories: {len(categories)}")
        print(f"  Products: {len(products)}")
        print(f"  Variations: {len(variations)}")
        print(f"  Reviews: {len(reviews)}")
        print(f"  Payments: {len(payments)}")
        print(f"  Orders: {len(orders)}")
        print(f"  Order Products: {len(order_products)}")
        print(f"  Carts: {len(carts)}")
        print(f"  Cart Items: {len(cart_items)}")
        print(f"  Organization: 1")
        print(f"  Hero Slides: {len(hero_slides)}")
        print(f"  About Contents: {len(about_contents)}")
        print(f"  Testimonials: {len(testimonials)}")
        print("\nYou can now test your application with realistic data!")

    except Exception as e:
        print(f"\nError occurred during data generation: {e}")
        print("Please check your Django setup and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
